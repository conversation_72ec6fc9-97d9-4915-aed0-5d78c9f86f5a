"use client";

import React, { useState, useEffect, useCallback } from "react";
import { Lead } from "@/types/lead";
import { createColumns } from "./columns";
import { DataTable, DataTableRef } from "./data-table";
import { logActivity, LogAction } from "@/lib/logger";
import { Button } from "@/components/ui/button";
import {
  Database,
  AlertCircle,
  Filter,
  Table as TableIcon,
  Map,
  Download,
  MoreHorizontal,
  Info,
  X,
  Search,
  ChevronDown,
  ChevronRight,
} from "lucide-react";
import Link from "next/link";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import LeadsMap from "@/components/LeadsMap";
import TableSkeleton from "@/components/TableSkeleton";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Toolt<PERSON>,
  Toolt<PERSON>Content,
  Too<PERSON><PERSON><PERSON>rovider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Collapsible, CollapsibleContent } from "@/components/ui/collapsible";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

// Types for pagination
interface PaginationInfo {
  page: number;
  pageSize: number;
  total: number;
  totalPages: number;
}

interface LeadsResponse {
  leads: Lead[];
  pagination?: PaginationInfo;
}

export default function LeadsPage() {
  const [leads, setLeads] = useState<Lead[]>([]);
  const [allLeads, setAllLeads] = useState<Lead[]>([]); // For map view and export
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeView, setActiveView] = useState<"table" | "map">("table");
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [filterCount, setFilterCount] = useState(0);
  const [expandedDuplicates, setExpandedDuplicates] = useState<Set<string>>(
    new Set()
  );
  const [showDuplicatesOnly, setShowDuplicatesOnly] = useState(false);

  // Pagination state
  const [pagination, setPagination] = useState<PaginationInfo>({
    page: 1,
    pageSize: 25,
    total: 0,
    totalPages: 0,
  });

  // Search and filter state
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState("created_at");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");

  // Reference to the DataTable component
  const dataTableRef = React.useRef<DataTableRef>(null);

  // Fetch leads with pagination
  const fetchLeads = useCallback(
    async (
      page: number = pagination.page,
      pageSize: number = pagination.pageSize,
      search: string = searchTerm,
      sortField: string = sortBy,
      sortDirection: "asc" | "desc" = sortOrder
    ) => {
      try {
        setLoading(true);

        // Build query parameters
        const params = new URLSearchParams({
          page: page.toString(),
          pageSize: pageSize.toString(),
          sortBy: sortField,
          sortOrder: sortDirection,
        });

        if (search) {
          params.append("search", search);
        }

        const res = await fetch(`/api/leads?${params.toString()}`);

        if (!res.ok) {
          throw new Error(`Failed to fetch leads: ${res.statusText}`);
        }

        const data: LeadsResponse = await res.json();

        if (!data.leads || !Array.isArray(data.leads)) {
          throw new Error(
            "Invalid response format: expected an array of leads"
          );
        }

        setLeads(data.leads);
        if (data.pagination) {
          setPagination(data.pagination);
        }
        setError(null);

        // Log activity without blocking
        logActivity(LogAction.LEAD_EXPORT, "Leads page viewed", {
          leads_count: data.leads.length,
          page,
          pageSize,
        }).catch(console.error);
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "An unknown error occurred";
        setError(errorMessage);

        // Log error without blocking
        logActivity(LogAction.LEAD_EXPORT, "Failed to load leads", {
          error: errorMessage,
        }).catch(console.error);
      } finally {
        setLoading(false);
      }
    },
    [pagination.page, pagination.pageSize, searchTerm, sortBy, sortOrder]
  );

  // Fetch all leads for map view and export (backward compatibility)
  const fetchAllLeads = useCallback(async () => {
    try {
      const res = await fetch("/api/leads"); // No pagination params = all leads
      if (!res.ok) {
        throw new Error(`Failed to fetch all leads: ${res.statusText}`);
      }
      const data = await res.json();
      if (data.leads && Array.isArray(data.leads)) {
        setAllLeads(data.leads);
      }
    } catch (err) {
      console.error("Failed to fetch all leads:", err);
    }
  }, []);

  // Initial load
  useEffect(() => {
    fetchLeads();
    fetchAllLeads();
  }, []);

  // Refetch when pagination, search, or sort changes
  useEffect(() => {
    fetchLeads();
  }, [pagination.page, pagination.pageSize, searchTerm, sortBy, sortOrder]);

  const handleExport = async () => {
    try {
      // Your existing export code...

      await logActivity(LogAction.LEAD_EXPORT, "Leads exported successfully", {
        format: "excel",
        leads_count: leads.length,
        export_timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error("Error exporting leads:", error);

      await logActivity(LogAction.LEAD_EXPORT, "Failed to export leads", {
        error: error instanceof Error ? error.message : "Unknown error",
      });
    }
  };

  if (loading && leads.length === 0) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex flex-col space-y-4">
          {/* Header skeleton */}
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div className="h-8 w-48 bg-muted animate-pulse rounded" />
            <div className="flex gap-2">
              <div className="h-10 w-32 bg-muted animate-pulse rounded" />
              <div className="h-10 w-24 bg-muted animate-pulse rounded" />
              <div className="h-10 w-24 bg-muted animate-pulse rounded" />
            </div>
          </div>

          {/* Table skeleton */}
          <TableSkeleton rows={10} columns={8} />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-6 py-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            {error}
            <Button
              variant="outline"
              className="mt-4"
              onClick={() => window.location.reload()}
            >
              Try Again
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // Calculate some statistics for the dashboard cards (use allLeads for accurate totals)
  const getLeadStats = () => {
    const dataToUse = allLeads.length > 0 ? allLeads : leads;
    if (!dataToUse.length)
      return { total: 0, withPlaceId: 0, withLocation: 0, approved: 0 };

    const withPlaceId = dataToUse.filter(
      (lead) => lead.google_place_id && lead.google_place_id !== "None"
    ).length;
    const withLocation = dataToUse.filter((lead) => lead.location).length;
    const approved = dataToUse.filter((lead) => lead.approved).length;

    return {
      total: pagination.total || dataToUse.length,
      withPlaceId,
      withLocation,
      approved,
    };
  };

  const stats = getLeadStats();

  // Process leads to identify duplicates and create display data
  const processLeadsWithDuplicates = () => {
    if (!leads.length) return [];

    // Group leads by google_place_id
    const leadGroups: { [key: string]: Lead[] } = {};
    const leadsWithoutPlaceId: Lead[] = [];

    leads.forEach((lead) => {
      if (lead.google_place_id && lead.google_place_id !== "None") {
        if (!leadGroups[lead.google_place_id]) {
          leadGroups[lead.google_place_id] = [];
        }
        leadGroups[lead.google_place_id].push(lead);
      } else {
        leadsWithoutPlaceId.push(lead);
      }
    });

    const processedLeads: (Lead & {
      isDuplicate?: boolean;
      isHiddenDuplicate?: boolean;
      duplicateCount?: number;
      duplicateGroupId?: string;
    })[] = [];

    // Process groups with duplicates
    Object.entries(leadGroups).forEach(([placeId, groupLeads]) => {
      if (groupLeads.length > 1) {
        // Sort by source priority: ERP first, then by creation date
        const sortedLeads = [...groupLeads].sort((a, b) => {
          if (a.source === "ERP" && b.source !== "ERP") return -1;
          if (a.source !== "ERP" && b.source === "ERP") return 1;
          return (
            new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
          );
        });

        const primaryLead = sortedLeads[0];
        const duplicateLeads = sortedLeads.slice(1);

        // Add primary lead with duplicate indicator
        processedLeads.push({
          ...primaryLead,
          isDuplicate: true,
          duplicateCount: duplicateLeads.length,
          duplicateGroupId: placeId,
        });

        // Add duplicate leads (hidden by default)
        duplicateLeads.forEach((lead) => {
          if (expandedDuplicates.has(placeId)) {
            processedLeads.push({
              ...lead,
              isDuplicate: true,
              isHiddenDuplicate: true,
              duplicateGroupId: placeId,
            });
          }
        });
      } else {
        // Single lead with place ID
        processedLeads.push(groupLeads[0]);
      }
    });

    // Add leads without place IDs (only if not filtering for duplicates only)
    if (!showDuplicatesOnly) {
      processedLeads.push(...leadsWithoutPlaceId);
    }

    // If showing duplicates only, filter to only include leads that are part of duplicate groups
    if (showDuplicatesOnly) {
      return processedLeads.filter((lead) => lead.isDuplicate);
    }

    return processedLeads;
  };

  const processedLeads = processLeadsWithDuplicates();

  // Debug: Log duplicate information
  const duplicateGroups = processedLeads.filter(
    (lead) => lead.isDuplicate && !lead.isHiddenDuplicate
  );
  if (duplicateGroups.length > 0) {
    console.log("Found duplicate groups:", duplicateGroups.length);
    console.log(
      "Duplicate leads:",
      duplicateGroups.map((lead) => ({
        name: lead.name,
        source: lead.source,
        google_place_id: lead.google_place_id,
        duplicateCount: lead.duplicateCount,
      }))
    );
  }

  // Toggle duplicate expansion
  const toggleDuplicateExpansion = (placeId: string) => {
    setExpandedDuplicates((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(placeId)) {
        newSet.delete(placeId);
      } else {
        newSet.add(placeId);
      }
      return newSet;
    });
  };

  // Expand all duplicates
  const expandAllDuplicates = () => {
    const allDuplicateGroupIds = new Set<string>();
    Object.keys(
      leads.reduce((groups: { [key: string]: Lead[] }, lead) => {
        if (lead.google_place_id && lead.google_place_id !== "None") {
          if (!groups[lead.google_place_id]) {
            groups[lead.google_place_id] = [];
          }
          groups[lead.google_place_id].push(lead);
        }
        return groups;
      }, {})
    ).forEach((placeId) => {
      const group = leads.filter((l) => l.google_place_id === placeId);
      if (group.length > 1) {
        allDuplicateGroupIds.add(placeId);
      }
    });
    setExpandedDuplicates(allDuplicateGroupIds);
  };

  // Collapse all duplicates
  const collapseAllDuplicates = () => {
    setExpandedDuplicates(new Set());
  };

  // Helper to clear all filters
  const clearAllFilters = () => {
    if (dataTableRef.current) {
      dataTableRef.current.clearAllFilters();
      setFilterCount(0);
    }
  };

  // The existing handleExport function is already defined above

  return (
    <div className="container mx-auto py-6">
      <div className="flex flex-col space-y-4">
        {/* Header with actions */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <h1 className="text-2xl font-bold">Leads Management</h1>

          <div className="flex flex-wrap gap-2">
            {/* View toggle */}
            <Tabs
              value={activeView}
              onValueChange={(value) => setActiveView(value as "table" | "map")}
              className="mr-2"
            >
              <TabsList className="grid w-[180px] grid-cols-2">
                <TabsTrigger value="table" className="flex items-center gap-1">
                  <TableIcon className="h-4 w-4" />
                  <span>Table</span>
                </TabsTrigger>
                <TabsTrigger value="map" className="flex items-center gap-1">
                  <Map className="h-4 w-4" />
                  <span>Map</span>
                </TabsTrigger>
              </TabsList>
            </Tabs>

            {/* Filter toggle */}
            <Button
              variant="outline"
              className="flex items-center gap-1"
              onClick={() => setIsFilterOpen(!isFilterOpen)}
            >
              <Filter className="h-4 w-4" />
              <span>Filters</span>
              {filterCount > 0 && (
                <span className="ml-1 rounded-full bg-primary text-primary-foreground w-5 h-5 text-xs flex items-center justify-center">
                  {filterCount}
                </span>
              )}
            </Button>

            {/* Duplicates filter toggle */}
            <Button
              variant={showDuplicatesOnly ? "default" : "outline"}
              className="flex items-center gap-1"
              onClick={() => setShowDuplicatesOnly(!showDuplicatesOnly)}
            >
              <AlertCircle className="h-4 w-4" />
              <span>Duplicates Only</span>
              {showDuplicatesOnly && (
                <span className="ml-1 rounded-full bg-white text-primary w-5 h-5 text-xs flex items-center justify-center">
                  {duplicateGroups.length}
                </span>
              )}
            </Button>

            {/* Actions dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="flex items-center gap-1">
                  <MoreHorizontal className="h-4 w-4" />
                  <span>Actions</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Lead Actions</DropdownMenuLabel>
                <DropdownMenuItem asChild>
                  <Link
                    href="/leads/bulk-place-id"
                    className="flex items-center gap-2"
                  >
                    <Search className="h-4 w-4" />
                    Bulk Place ID Assignment
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link
                    href="/leads/erp-sync"
                    className="flex items-center gap-2"
                  >
                    <Database className="h-4 w-4" />
                    ERP Sync
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={handleExport}
                  className="flex items-center gap-2"
                >
                  <Download className="h-4 w-4" />
                  Export All Leads
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={clearAllFilters}
                  className="flex items-center gap-2"
                >
                  <X className="h-4 w-4" />
                  Clear All Filters
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuLabel>Duplicate Actions</DropdownMenuLabel>
                <DropdownMenuItem
                  onClick={expandAllDuplicates}
                  className="flex items-center gap-2"
                  disabled={duplicateGroups.length === 0}
                >
                  <ChevronDown className="h-4 w-4" />
                  Expand All Duplicates
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={collapseAllDuplicates}
                  className="flex items-center gap-2"
                  disabled={expandedDuplicates.size === 0}
                >
                  <ChevronRight className="h-4 w-4" />
                  Collapse All Duplicates
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Stats summary - simplified */}
        {leads.length > 0 && (
          <div className="flex flex-wrap gap-4 mb-4 text-sm text-muted-foreground">
            {showDuplicatesOnly ? (
              <>
                <span>
                  <strong>{duplicateGroups.length}</strong> duplicate groups
                </span>
                <span>
                  <strong>{processedLeads.length}</strong> total duplicate leads
                </span>
                <span>
                  <strong>{expandedDuplicates.size}</strong> groups expanded
                </span>
              </>
            ) : (
              <>
                <span>
                  <strong>{stats.total}</strong> total leads
                  {pagination.total > 0 &&
                    pagination.total !== leads.length && (
                      <span className="text-muted-foreground">
                        {" "}
                        (showing {leads.length} of {pagination.total})
                      </span>
                    )}
                </span>
                {duplicateGroups.length > 0 && (
                  <span>
                    <strong>{duplicateGroups.length}</strong> duplicate groups
                  </span>
                )}
                {stats.withPlaceId > 0 && (
                  <span>
                    <strong>{stats.withPlaceId}</strong> with Google Place ID
                  </span>
                )}
                {stats.withLocation > 0 && (
                  <span>
                    <strong>{stats.withLocation}</strong> with location data
                  </span>
                )}
                {stats.approved > 0 && (
                  <span>
                    <strong>{stats.approved}</strong> approved
                  </span>
                )}
              </>
            )}
          </div>
        )}

        {/* Color legend */}
        <div className="flex flex-wrap gap-4 mb-4">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 rounded bg-green-50 border border-green-200"></div>
                  <span className="text-sm">Kunde</span>
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p>Customer leads are highlighted in green</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 rounded bg-red-50 border border-red-200"></div>
                  <span className="text-sm">Werbekunde</span>
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p>Advertising customer leads are highlighted in red</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 rounded bg-blue-50 border border-blue-200"></div>
                  <span className="text-sm">No Address Group</span>
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p>Leads without an address group are highlighted in blue</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="flex items-center gap-2">
                  <AlertCircle className="h-4 w-4 text-red-500" />
                  <span className="text-sm">Duplicates</span>
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p>
                  Red pin indicates leads with duplicates based on Google Place
                  ID. Click to expand/collapse duplicates.
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="sm" className="h-6 gap-1">
                  <Info className="h-3 w-3" />
                  <span className="text-xs">About Colors</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent className="max-w-sm">
                <p>
                  Rows are colored based on the Address Group Description field
                  to help you quickly identify different types of leads.
                  Duplicate leads are indicated with a red pin icon and can be
                  expanded to show hidden duplicates.
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>

        {/* Collapsible filter panel */}
        <Collapsible
          open={isFilterOpen}
          onOpenChange={setIsFilterOpen}
          className="mb-4"
        >
          <CollapsibleContent className="p-4 border rounded-md bg-muted/10 space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Location Filters */}
              <div className="space-y-3">
                <h3 className="text-sm font-medium">Location Filters</h3>
                <div className="space-y-2">
                  <div className="flex flex-col gap-1.5">
                    <Label htmlFor="canton-filter" className="text-xs">
                      Canton
                    </Label>
                    <Select
                      defaultValue="all"
                      onValueChange={(value) => {
                        if (dataTableRef.current) {
                          dataTableRef.current.setFilter(
                            "canton",
                            value === "all" ? null : value
                          );
                          setFilterCount(
                            dataTableRef.current.getActiveFilters().length
                          );
                        }
                      }}
                    >
                      <SelectTrigger id="canton-filter" className="h-8">
                        <SelectValue placeholder="All Cantons" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Cantons</SelectItem>
                        {Array.from(
                          new Set(
                            (allLeads.length > 0 ? allLeads : leads)
                              .map((lead) => lead.canton)
                              .filter(Boolean)
                          )
                        )
                          .sort()
                          .map((canton) => (
                            <SelectItem
                              key={String(canton)}
                              value={String(canton)}
                            >
                              {String(canton)}
                            </SelectItem>
                          ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="flex flex-col gap-1.5">
                    <Label htmlFor="postal-code-filter" className="text-xs">
                      Postal Code
                    </Label>
                    <Input
                      id="postal-code-filter"
                      placeholder="e.g. 8001"
                      className="h-8"
                      onChange={(e) => {
                        if (dataTableRef.current) {
                          const value = e.target.value;
                          dataTableRef.current.setFilter(
                            "postal_code",
                            value || null
                          );
                          setFilterCount(
                            dataTableRef.current.getActiveFilters().length
                          );
                        }
                      }}
                    />
                  </div>
                </div>
              </div>

              {/* Business Filters */}
              <div className="space-y-3">
                <h3 className="text-sm font-medium">Business Filters</h3>
                <div className="space-y-2">
                  <div className="flex flex-col gap-1.5">
                    <Label htmlFor="business-status-filter" className="text-xs">
                      Business Status
                    </Label>
                    <Select
                      defaultValue="all"
                      onValueChange={(value) => {
                        if (dataTableRef.current) {
                          dataTableRef.current.setFilter(
                            "business_status",
                            value === "all" ? null : value
                          );
                          setFilterCount(
                            dataTableRef.current.getActiveFilters().length
                          );
                        }
                      }}
                    >
                      <SelectTrigger
                        id="business-status-filter"
                        className="h-8"
                      >
                        <SelectValue placeholder="All Statuses" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Statuses</SelectItem>
                        {Array.from(
                          new Set(
                            (allLeads.length > 0 ? allLeads : leads)
                              .map((lead) => lead.business_status)
                              .filter(Boolean)
                          )
                        )
                          .sort()
                          .map((status) => (
                            <SelectItem
                              key={String(status)}
                              value={String(status)}
                            >
                              {String(status)
                                .replace(/_/g, " ")
                                .toLowerCase()
                                .split(" ")
                                .map(
                                  (word) =>
                                    word.charAt(0).toUpperCase() + word.slice(1)
                                )
                                .join(" ")}
                            </SelectItem>
                          ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="flex flex-col gap-1.5">
                    <Label htmlFor="address-group-filter" className="text-xs">
                      Address Group
                    </Label>
                    <Select
                      defaultValue="all"
                      onValueChange={(value) => {
                        if (dataTableRef.current) {
                          dataTableRef.current.setFilter(
                            "address_group_description",
                            value === "all" ? null : value
                          );
                          setFilterCount(
                            dataTableRef.current.getActiveFilters().length
                          );
                        }
                      }}
                    >
                      <SelectTrigger id="address-group-filter" className="h-8">
                        <SelectValue placeholder="All Groups" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Groups</SelectItem>
                        <SelectItem value="__empty__">No Group</SelectItem>
                        {Array.from(
                          new Set(
                            (allLeads.length > 0 ? allLeads : leads)
                              .map((lead) => lead.address_group_description)
                              .filter(Boolean)
                          )
                        )
                          .sort()
                          .map((group) => (
                            <SelectItem
                              key={String(group)}
                              value={String(group)}
                            >
                              {String(group)}
                            </SelectItem>
                          ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              {/* Data Quality Filters */}
              <div className="space-y-3">
                <h3 className="text-sm font-medium">Data Quality Filters</h3>
                <div className="space-y-2">
                  <div className="flex flex-col gap-1.5">
                    <Label htmlFor="source-filter" className="text-xs">
                      Data Source
                    </Label>
                    <Select
                      defaultValue="all"
                      onValueChange={(value) => {
                        if (dataTableRef.current) {
                          dataTableRef.current.setFilter(
                            "source",
                            value === "all" ? null : value
                          );
                          setFilterCount(
                            dataTableRef.current.getActiveFilters().length
                          );
                        }
                      }}
                    >
                      <SelectTrigger id="source-filter" className="h-8">
                        <SelectValue placeholder="All Sources" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Sources</SelectItem>
                        {Array.from(
                          new Set(
                            (allLeads.length > 0 ? allLeads : leads)
                              .map((lead) => lead.source)
                              .filter(Boolean)
                          )
                        )
                          .sort()
                          .map((source) => (
                            <SelectItem
                              key={String(source)}
                              value={String(source)}
                            >
                              {String(source) === "PlacesAPI"
                                ? "Google Places API"
                                : String(source) === "ERP"
                                ? "ERP System"
                                : String(source) === "Manual"
                                ? "Manually Added"
                                : String(source)}
                            </SelectItem>
                          ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="flex flex-col gap-1.5 mt-4">
                    <Label htmlFor="time-period-filter" className="text-xs">
                      Added Time Period
                    </Label>
                    <Select
                      defaultValue="all"
                      onValueChange={(value) => {
                        if (dataTableRef.current) {
                          dataTableRef.current.setFilter(
                            "created_at",
                            value === "all" ? null : value
                          );
                          setFilterCount(
                            dataTableRef.current.getActiveFilters().length
                          );
                        }
                      }}
                    >
                      <SelectTrigger id="time-period-filter" className="h-8">
                        <SelectValue placeholder="All Time" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Time</SelectItem>
                        <SelectItem value="7">Last 7 Days</SelectItem>
                        <SelectItem value="30">Last 30 Days</SelectItem>
                        <SelectItem value="90">Last 90 Days</SelectItem>
                        <SelectItem value="365">Last Year</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-end mt-4">
              <Button
                variant="outline"
                size="sm"
                onClick={clearAllFilters}
                className="flex items-center gap-1"
              >
                <X className="h-3.5 w-3.5" />
                Clear Filters
              </Button>
            </div>
          </CollapsibleContent>
        </Collapsible>

        {/* Main content */}
        {leads.length === 0 ? (
          <div className="bg-muted/30 p-8 rounded-lg flex flex-col items-center justify-center">
            <p className="text-lg mb-4">No leads found</p>
            <p className="text-sm text-muted-foreground mb-6">
              Import leads or sync with ERP to get started
            </p>
          </div>
        ) : showDuplicatesOnly && duplicateGroups.length === 0 ? (
          <div className="bg-muted/30 p-8 rounded-lg flex flex-col items-center justify-center">
            <AlertCircle className="h-12 w-12 text-muted-foreground mb-4" />
            <p className="text-lg mb-4">No duplicate leads found</p>
            <p className="text-sm text-muted-foreground mb-6">
              All leads have unique Google Place IDs or no Place ID at all.
            </p>
            <Button
              variant="outline"
              onClick={() => setShowDuplicatesOnly(false)}
              className="flex items-center gap-2"
            >
              <X className="h-4 w-4" />
              Show All Leads
            </Button>
          </div>
        ) : (
          <Tabs value={activeView} className="w-full">
            <TabsContent value="table" className="mt-0">
              <DataTable
                ref={dataTableRef}
                columns={createColumns(
                  toggleDuplicateExpansion,
                  expandedDuplicates
                )}
                data={processedLeads}
              />
            </TabsContent>
            <TabsContent value="map" className="mt-0">
              <LeadsMap leads={allLeads.length > 0 ? allLeads : leads} />
            </TabsContent>
          </Tabs>
        )}
      </div>
    </div>
  );
}
